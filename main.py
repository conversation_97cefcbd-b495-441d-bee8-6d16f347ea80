import sys
import webbrowser
import json
import os
import re
import time
import datetime
from urllib.parse import urlparse, parse_qs, quote_plus, unquote_plus
from collections import deque

try:
    import requests
except ImportError:
    print("Error: The 'requests' library is not installed. Please install it using 'pip install requests'")
    sys.exit(1)

try:
    from PySide6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QTabWidget, QPushButton, QTextEdit, QLineEdit, QLabel,
        QFileDialog, QMessageBox, QScrollArea, QFrame, QSpacerItem,
        QSizePolicy, QPlainTextEdit, QComboBox
    )
    from PySide6.QtCore import Qt, QThread, Signal, Slot, QSize
    from PySide6.QtGui import QClipboard, QIcon # QIcon is optional for window icon
except ImportError:
    print("Error: The 'PySide6' library is not installed. Please install it using 'pip install PySide6'")
    sys.exit(1)

# --- Configuration ---
ADJUST_WRAPPER_URL = "https://app.adjust.com/10y4cdbj"
BESITOS_API_BASE_URL = "https://wall.besitos.ai/api/v2"
BESITOS_PARTNER_ID = "m1n1dwunbl7w"
BESITOS_CHANNEL = "games"
MAX_LOG_ENTRIES = 150

# --- Elegant Warm Theme QSS (Qt Style Sheet) ---
WARM_THEME_QSS = """
    QMainWindow, QWidget {
        background-color: #432818;
        color: #ffe6a7;
        font-size: 10pt;
        font-family: "Segoe UI", Arial, sans-serif;
    }

    QTabWidget::pane {
        border-top: 3px solid #bb9457;
        background-color: #432818;
        border-radius: 0px 0px 8px 8px;
    }

    QTabBar::tab {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #99582a, stop: 1 #6f1d1b);
        color: #ffe6a7;
        padding: 12px 24px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        min-width: 140px;
        margin-right: 2px;
        font-weight: 500;
    }

    QTabBar::tab:selected {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #bb9457, stop: 1 #99582a);
        color: #ffe6a7;
        font-weight: bold;
        border-bottom: 3px solid #ffe6a7;
    }

    QTabBar::tab:hover:!selected {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #bb9457, stop: 1 #6f1d1b);
    }

    QPushButton {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #bb9457, stop: 1 #99582a);
        border: 2px solid #6f1d1b;
        padding: 10px 18px;
        min-height: 24px;
        border-radius: 6px;
        color: #ffe6a7;
        font-weight: 600;
        font-size: 9pt;
    }

    QPushButton:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #ffe6a7, stop: 1 #bb9457);
        color: #432818;
        border: 2px solid #ffe6a7;
    }

    QPushButton:pressed {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #99582a, stop: 1 #6f1d1b);
        color: #ffe6a7;
        border: 2px solid #432818;
    }

    QPushButton:disabled {
        background-color: #6f1d1b;
        color: #99582a;
        border: 2px solid #432818;
    }

    QLineEdit, QTextEdit, QPlainTextEdit {
        background-color: #6f1d1b;
        border: 2px solid #99582a;
        padding: 8px;
        border-radius: 6px;
        color: #ffe6a7;
        selection-background-color: #bb9457;
        selection-color: #432818;
    }

    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
        border: 2px solid #bb9457;
        background-color: #432818;
    }

    QTextEdit:read-only, QPlainTextEdit:read-only {
        background-color: #432818;
        border: 2px solid #6f1d1b;
    }

    QLabel {
        color: #ffe6a7;
        font-weight: 500;
    }

    QScrollArea {
        border: 2px solid #6f1d1b;
        background-color: #432818;
        border-radius: 6px;
    }

    QScrollBar:vertical {
        border: 1px solid #6f1d1b;
        background: #432818;
        width: 16px;
        margin: 0px;
        border-radius: 8px;
    }

    QScrollBar::handle:vertical {
        background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                   stop: 0 #bb9457, stop: 1 #99582a);
        min-height: 24px;
        border-radius: 6px;
        margin: 2px;
    }

    QScrollBar::handle:vertical:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                   stop: 0 #ffe6a7, stop: 1 #bb9457);
    }

    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        height: 0px;
    }

    QScrollBar:horizontal {
        border: 1px solid #6f1d1b;
        background: #432818;
        height: 16px;
        margin: 0px;
        border-radius: 8px;
    }

    QScrollBar::handle:horizontal {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #bb9457, stop: 1 #99582a);
        min-width: 24px;
        border-radius: 6px;
        margin: 2px;
    }

    QScrollBar::handle:horizontal:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                   stop: 0 #ffe6a7, stop: 1 #bb9457);
    }

    QGroupBox, QFrame[frameShape="StyledPanel"] {
        border: 2px solid #bb9457;
        border-radius: 8px;
        margin-top: 12px;
        padding: 12px;
        background-color: rgba(111, 29, 27, 0.3);
    }

    QGroupBox::title {
        subcontrol-origin: margin;
        subcontrol-position: top left;
        padding: 4px 8px;
        color: #ffe6a7;
        font-weight: bold;
        background-color: #bb9457;
        border-radius: 4px;
    }

    /* Specific styling for special elements */
    #ActivityLog, #AdjustUrlInput, #OfferJsonDisplay {
        font-family: "Consolas", "Courier New", monospace;
        font-size: 9pt;
        background-color: #6f1d1b;
        border: 2px solid #99582a;
    }

    /* Status message styling */
    .success-message {
        color: #ffe6a7;
        background-color: #99582a;
        border: 1px solid #bb9457;
        border-radius: 4px;
        padding: 4px 8px;
    }

    .error-message {
        color: #ffe6a7;
        background-color: #6f1d1b;
        border: 1px solid #432818;
        border-radius: 4px;
        padding: 4px 8px;
    }

    .warning-message {
        color: #432818;
        background-color: #ffe6a7;
        border: 1px solid #bb9457;
        border-radius: 4px;
        padding: 4px 8px;
    }
"""

# --- Worker Threads ---
class GenericWorker(QThread):
    result = Signal(object)
    error = Signal(str)
    finished = Signal() # To re-enable buttons etc.

    def __init__(self, function, *args, **kwargs):
        super().__init__()
        self.function = function
        self.args = args
        self.kwargs = kwargs

    def run(self):
        try:
            res = self.function(*self.args, **self.kwargs)
            self.result.emit(res)
        except Exception as e:
            self.error.emit(str(e))
        finally:
            self.finished.emit()

# --- Main Application Window ---
class MobileMarketingToolkit(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 Mobile Marketing Toolkit - Professional Edition")
        self.setGeometry(100, 100, 1500, 950) # Increased size for better layout
        self.setStyleSheet(WARM_THEME_QSS) # Apply warm theme

        # Central Widget and Tab Setup
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout(self.central_widget) # Main layout is horizontal

        self.tab_widget = QTabWidget()
        self.main_layout.addWidget(self.tab_widget, 3) # Tabs take 3/4 of space

        # Activity Log (as a separate panel on the right)
        self._create_activity_log_panel()
        self.main_layout.addWidget(self.activity_log_group, 1) # Log takes 1/4 of space

        # Initialize Tabs
        self._create_adjust_toolkit_tab()
        self._create_offer_wall_tab()

        self._log_activity("Application started.", "INFO")

    def _create_activity_log_panel(self):
        self.activity_log_group = QFrame() # Using QFrame for styling flexibility
        self.activity_log_group.setObjectName("ActivityLogGroup")
        log_panel_layout = QVBoxLayout(self.activity_log_group)
        log_panel_layout.setContentsMargins(5,5,5,5)

        log_title = QLabel("📊 Activity Log")
        log_title.setStyleSheet("font-size: 13pt; font-weight: bold; color: #ffe6a7; background-color: #bb9457; padding: 8px; border-radius: 6px; margin-bottom: 8px;")
        log_panel_layout.addWidget(log_title)

        self.activity_log_text = QPlainTextEdit() # QPlainTextEdit is better for logs
        self.activity_log_text.setObjectName("ActivityLog")
        self.activity_log_text.setReadOnly(True)
        self.activity_log_text.setLineWrapMode(QPlainTextEdit.LineWrapMode.WidgetWidth)
        log_panel_layout.addWidget(self.activity_log_text)
        self.log_entries = deque(maxlen=MAX_LOG_ENTRIES)

    def _log_activity(self, message, level="INFO"):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        self.log_entries.append(log_entry)
        
        # Update QPlainTextEdit efficiently
        self.activity_log_text.setPlainText("\n".join(self.log_entries))
        self.activity_log_text.verticalScrollBar().setValue(self.activity_log_text.verticalScrollBar().maximum()) # Scroll to bottom
        
        if level == "ERROR":
            print(f"ERROR: {message}")

    def _create_scrollable_area(self):
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area_content = QWidget()
        scroll_area.setWidget(scroll_area_content)
        content_layout = QVBoxLayout(scroll_area_content)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignTop) # Content flows from top
        return scroll_area, content_layout

    # --- Adjust URL Toolkit Tab ---
    def _create_adjust_toolkit_tab(self):
        self.adjust_tab = QWidget()
        self.tab_widget.addTab(self.adjust_tab, "🔗 Adjust URL Toolkit")
        adjust_tab_layout = QVBoxLayout(self.adjust_tab)

        # Input Frame
        input_group = QFrame()
        input_group.setFrameShape(QFrame.Shape.StyledPanel)
        input_group_layout = QVBoxLayout(input_group)
        
        input_label = QLabel("Paste Adjust tracking links (one per line):")
        input_group_layout.addWidget(input_label)

        self.adjust_url_input = QTextEdit()
        self.adjust_url_input.setObjectName("AdjustUrlInput")
        self.adjust_url_input.setPlaceholderText("e.g., https://app.adjust.com/tracker?param=value...")
        self.adjust_url_input.setMinimumHeight(100)
        input_group_layout.addWidget(self.adjust_url_input)

        button_bar = QHBoxLayout()
        self.adj_paste_btn = QPushButton("📋 Paste from Clipboard")
        self.adj_paste_btn.clicked.connect(self.adj_paste_from_clipboard)
        button_bar.addWidget(self.adj_paste_btn)

        self.adj_clear_btn = QPushButton("🗑️ Clear Input & Results")
        self.adj_clear_btn.clicked.connect(self.adj_clear_input)
        button_bar.addWidget(self.adj_clear_btn)

        button_bar.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))

        self.adj_process_btn = QPushButton("⚡ Process URLs & Extract Callbacks")
        self.adj_process_btn.clicked.connect(self.adj_process_urls)
        button_bar.addWidget(self.adj_process_btn)
        input_group_layout.addLayout(button_bar)
        adjust_tab_layout.addWidget(input_group)

        # Results Frame
        results_group_title = QLabel("Processed Links & Callbacks")
        results_group_title.setStyleSheet("font-size: 12pt; font-weight: bold; margin-top: 15px; color: #ffe6a7; background-color: #99582a; padding: 8px; border-radius: 6px;")
        adjust_tab_layout.addWidget(results_group_title)

        self.adjust_results_scroll_area, self.adjust_results_layout = self._create_scrollable_area()
        adjust_tab_layout.addWidget(self.adjust_results_scroll_area)

    def adj_paste_from_clipboard(self):
        clipboard = QApplication.clipboard()
        text = clipboard.text()
        if text:
            self.adjust_url_input.append(text) # append to add newline if needed
            self._log_activity("Pasted content from clipboard into Adjust URL input.", "SUCCESS")
            QMessageBox.information(self, "Paste Success", "Content pasted from clipboard.")
        else:
            self._log_activity("Clipboard is empty or contains no text.", "WARNING")
            QMessageBox.information(self, "Paste Info", "Clipboard is empty or contains no text.")

    def adj_clear_input(self):
        self.adjust_url_input.clear()
        self._clear_layout(self.adjust_results_layout)
        self._log_activity("Adjust URL input and results cleared.")

    def adj_process_urls(self):
        urls_text = self.adjust_url_input.toPlainText().strip()
        if not urls_text:
            QMessageBox.warning(self, "Input Required", "Please input Adjust URLs to process.")
            return

        input_urls = [url.strip() for url in urls_text.splitlines() if url.strip()]
        self._clear_layout(self.adjust_results_layout)
        self._log_activity(f"Starting processing of {len(input_urls)} Adjust URLs.")

        self.adj_process_btn.setEnabled(False)
        # Using a worker would be for a very long list, for now, direct processing with UI updates
        for i, original_url in enumerate(input_urls):
            self._display_single_adj_url_processing(original_url, i)
        self.adj_process_btn.setEnabled(True)
        self._log_activity("Finished processing all Adjust URLs.")

    def _display_single_adj_url_processing(self, original_url, index):
        url_frame = QFrame()
        url_frame.setFrameShape(QFrame.Shape.StyledPanel)
        url_frame_layout = QVBoxLayout(url_frame)

        url_frame_layout.addWidget(QLabel(f"<b>Original URL ({index+1}):</b> {original_url[:100]}{'...' if len(original_url) > 100 else ''}"))

        parsed_url = urlparse(original_url)
        if not (parsed_url.scheme in ['http', 'https'] and parsed_url.netloc):
            err_label = QLabel("Error: Invalid URL format.")
            err_label.setStyleSheet("color: #ffe6a7; background-color: #6f1d1b; padding: 6px; border-radius: 4px; font-weight: bold;")
            url_frame_layout.addWidget(err_label)
            self._log_activity(f"URL '{original_url}' is invalid.", "ERROR")
            self.adjust_results_layout.addWidget(url_frame)
            return

        query_params = parse_qs(parsed_url.query)
        
        app_name = "Unknown App"
        adj_t_token = query_params.get('adj_t', [None])[0]
        if adj_t_token: app_name = f"App (token: {adj_t_token})"
        elif "adjust.com" in parsed_url.netloc and parsed_url.path and parsed_url.path != "/":
            app_name = parsed_url.path.split('/')[1] if len(parsed_url.path.split('/')) > 1 else parsed_url.netloc
        else: app_name = parsed_url.netloc
        url_frame_layout.addWidget(QLabel(f"Identified App/Game: {app_name}"))

        install_callback_val = query_params.get('install_callback', [None])[0]
        event_callbacks = {k: v[0] for k, v in query_params.items() if k.startswith('event_callback_')}
        generated_any_link = False
        event_link_details = []

        if install_callback_val:
            generated_any_link = True
            wrapped_link = f"{ADJUST_WRAPPER_URL}?install_callback={quote_plus(install_callback_val)}"
            self._create_pyside_link_display(url_frame_layout, "Install Callback Link", wrapped_link, "Install")
            self._log_activity(f"Generated install callback for {original_url}", "INFO")

        if event_callbacks:
            generated_any_link = True
            url_frame_layout.addWidget(QLabel("<b>Event Callback Links:</b>"))
            for param_name, callback_val in event_callbacks.items():
                event_id = param_name.replace('event_callback_', '')
                event_name_display = f"Event ({event_id if event_id else 'default'})"
                wrapped_link = f"{ADJUST_WRAPPER_URL}?{quote_plus(param_name)}={quote_plus(callback_val)}"
                event_link_details.append({"name": event_name_display, "link": wrapped_link})
                self._create_pyside_link_display(url_frame_layout, event_name_display, wrapped_link, "Event")
                self._log_activity(f"Generated {event_name_display} callback for {original_url}", "INFO")
            
            if len(event_link_details) > 1:
                fire_all_button = QPushButton("🎯 Fire All Events (Smart Order)")
                fire_all_button.clicked.connect(lambda checked=False, links=list(event_link_details): self.adj_fire_all_events(links))
                url_frame_layout.addWidget(fire_all_button, alignment=Qt.AlignmentFlag.AlignLeft)


        if not generated_any_link:
            no_cb_label = QLabel("No processable install or event callbacks found.")
            no_cb_label.setStyleSheet("color: #432818; background-color: #ffe6a7; padding: 6px; border-radius: 4px; font-weight: bold;")
            url_frame_layout.addWidget(no_cb_label)
            self._log_activity(f"No callbacks found for URL '{original_url}'.", "WARNING")
        
        self.adjust_results_layout.addWidget(url_frame)

    def _create_pyside_link_display(self, parent_layout, link_name, link_url, link_type_prefix):
        link_group_layout = QHBoxLayout()
        link_group_layout.addWidget(QLabel(f"{link_name}:"))
        
        link_text = QLineEdit(link_url)
        link_text.setReadOnly(True)
        link_group_layout.addWidget(link_text, 1) # Expandable text field

        copy_button = QPushButton("📋 Copy")
        copy_button.setFixedWidth(90)
        copy_button.clicked.connect(lambda: self.copy_to_clipboard(link_url, f"{link_type_prefix} link"))
        link_group_layout.addWidget(copy_button)

        fire_button = QPushButton(f"🚀 Fire {link_type_prefix}")
        fire_button.setFixedWidth(120)
        fire_button.clicked.connect(lambda: self.fire_link(link_url, f"{link_type_prefix} link"))
        link_group_layout.addWidget(fire_button)
        parent_layout.addLayout(link_group_layout)

    def adj_fire_all_events(self, event_link_details):
        if not event_link_details: return
        num_events = len(event_link_details)
        ordered_links_to_fire = []
        N = min(2, num_events) # Number of last events to fire first, ensure N <= num_events

        if num_events <= N:
            ordered_links_to_fire = [details['link'] for details in reversed(event_link_details)]
        else:
            for i in range(num_events - 1, num_events - 1 - N, -1): # Last N events (reversed)
                ordered_links_to_fire.append(event_link_details[i]['link'])
            if 0 not in [num_events - 1 - j for j in range(N)]: # Ensure first element is not already added
                 ordered_links_to_fire.append(event_link_details[0]['link']) # First event
            
            # Middle events (excluding first and last N)
            start_middle = 1
            end_middle = num_events - N
            for i in range(start_middle, end_middle):
                 if event_link_details[i]['link'] not in ordered_links_to_fire:
                    ordered_links_to_fire.append(event_link_details[i]['link'])
        
        self._log_activity(f"Starting to fire {len(ordered_links_to_fire)} events in smart order.", "INFO")
        self.sequential_fire_worker = GenericWorker(self._fire_links_sequentially, ordered_links_to_fire, "Event")
        # self.sequential_fire_worker.finished.connect(lambda: self.adj_process_btn.setEnabled(True)) # If main btn disabled
        self.sequential_fire_worker.start()

    def _fire_links_sequentially(self, links, link_type_name):
        delay_between_fires = 1.5 # seconds
        for i, link_url in enumerate(links):
            self.fire_link(link_url, f"{link_type_name} {i+1}/{len(links)}") # fire_link logs itself
            if i < len(links) - 1:
                time.sleep(delay_between_fires)
        self._log_activity(f"Finished firing all {len(links)} {link_type_name}s.", "INFO")
        return "Firing complete" # For GenericWorker if it needs to emit something


    # --- Offer Wall Processor Tab ---
    def _create_offer_wall_tab(self):
        self.offer_wall_tab = QWidget()
        self.tab_widget.addTab(self.offer_wall_tab, "💰 Offer Wall Processor")
        offer_wall_tab_layout = QVBoxLayout(self.offer_wall_tab)

        # Data Input Frame
        data_input_group = QFrame()
        data_input_group.setFrameShape(QFrame.Shape.StyledPanel)
        data_input_layout = QVBoxLayout(data_input_group)

        # Upload JSON
        upload_layout = QHBoxLayout()
        self.ow_upload_btn = QPushButton("📁 Upload JSON Offer File")
        self.ow_upload_btn.clicked.connect(self.ow_upload_json)
        upload_layout.addWidget(self.ow_upload_btn)
        self.ow_json_filename_label = QLabel("No file uploaded.")
        upload_layout.addWidget(self.ow_json_filename_label, 1)
        data_input_layout.addLayout(upload_layout)

        # Fetch by SID
        fetch_sid_layout = QHBoxLayout()
        fetch_sid_layout.addWidget(QLabel(f"🆔 Subscriber ID (SID) for Besitos Wall (partner: {BESITOS_PARTNER_ID}, channel: {BESITOS_CHANNEL}):"))
        self.ow_sid_entry = QLineEdit()
        self.ow_sid_entry.setPlaceholderText("Enter SID")
        fetch_sid_layout.addWidget(self.ow_sid_entry)

        # User Agent Selection
        self.ow_user_agent_combo = QComboBox()
        self.ow_user_agent_combo.addItems(["📱 iOS", "🤖 Android"])
        self.ow_user_agent_combo.setFixedWidth(120)
        fetch_sid_layout.addWidget(self.ow_user_agent_combo)

        # Offer Limit Selection
        fetch_sid_layout.addWidget(QLabel("Limit:"))
        self.ow_limit_combo = QComboBox()
        self.ow_limit_combo.addItems(["50", "100", "200", "500", "1000"])
        self.ow_limit_combo.setCurrentText("500")
        self.ow_limit_combo.setFixedWidth(80)
        fetch_sid_layout.addWidget(self.ow_limit_combo)

        self.ow_fetch_sid_btn = QPushButton("🌐 Fetch Offers by SID")
        self.ow_fetch_sid_btn.clicked.connect(self.ow_fetch_by_sid)
        fetch_sid_layout.addWidget(self.ow_fetch_sid_btn)
        data_input_layout.addLayout(fetch_sid_layout)
        
        info_note = QLabel("Note: Redirect tracing uses Python's 'requests' (handles HTTP/S). Non-HTTP schemes (e.g., market://) are parsed if final, but not actively followed.")
        info_note.setStyleSheet("color: #432818; background-color: #ffe6a7; padding: 8px; border-radius: 6px; font-style: italic; font-weight: 500;")
        info_note.setWordWrap(True)
        data_input_layout.addWidget(info_note)

        offer_wall_tab_layout.addWidget(data_input_group)
        
        # Offer Display Frame
        offers_display_title = QLabel("Offers & Goals")
        offers_display_title.setStyleSheet("font-size: 12pt; font-weight: bold; margin-top: 15px; color: #ffe6a7; background-color: #99582a; padding: 8px; border-radius: 6px;")
        offer_wall_tab_layout.addWidget(offers_display_title)

        self.ow_offers_scroll_area, self.ow_offers_layout = self._create_scrollable_area()
        offer_wall_tab_layout.addWidget(self.ow_offers_scroll_area)
        
        self.loaded_offers_data = []

    def _parse_offers_from_json(self, data, filename):
        """
        Robust JSON parser that handles multiple offer wall formats:
        - Ayet: {"offers_array": [...]}
        - Besitos: {"offers": [...]}
        - Gravypass: {"offers": [...]}
        - Monlix: [...]  (direct array)
        - And other variations
        """
        offers = []

        try:
            # Handle direct array format (like Monlix)
            if isinstance(data, list):
                offers = data
                self._log_activity(f"Detected direct array format with {len(offers)} offers.", "INFO")

            # Handle object formats
            elif isinstance(data, dict):
                # Try different possible keys for offers
                possible_keys = ['offers', 'offers_array', 'data', 'results', 'items']

                for key in possible_keys:
                    if key in data and isinstance(data[key], list):
                        offers = data[key]
                        self._log_activity(f"Detected '{key}' format with {len(offers)} offers.", "INFO")
                        break

                # If no offers found in standard keys, check if the object itself contains offer-like data
                if not offers and self._looks_like_offer(data):
                    offers = [data]  # Single offer wrapped in array
                    self._log_activity("Detected single offer format.", "INFO")

                # If still no offers, try to find nested structures
                if not offers:
                    for key, value in data.items():
                        if isinstance(value, list) and value and self._looks_like_offer(value[0]):
                            offers = value
                            self._log_activity(f"Found offers in nested key '{key}' with {len(offers)} offers.", "INFO")
                            break

            if not offers:
                raise ValueError("No valid offers found in JSON. Supported formats: direct array, {offers: []}, {offers_array: []}, etc.")

            # Normalize offer data to ensure consistent structure
            normalized_offers = []
            for offer in offers:
                if isinstance(offer, dict):
                    normalized_offers.append(self._normalize_offer_data(offer))

            return normalized_offers

        except Exception as e:
            raise ValueError(f"Error parsing offers: {str(e)}")

    def _looks_like_offer(self, obj):
        """Check if an object looks like an offer by checking for common offer fields"""
        if not isinstance(obj, dict):
            return False

        # Common offer fields across different platforms
        offer_indicators = ['name', 'title', 'id', 'offer_id', 'url', 'click_url', 'tracking_link', 'payout', 'amount', 'goals']
        return any(key in obj for key in offer_indicators)

    def _normalize_offer_data(self, offer):
        """Normalize offer data to have consistent field names"""
        normalized = offer.copy()

        # Normalize name/title
        if 'title' in offer and 'name' not in offer:
            normalized['name'] = offer['title']
        elif 'name' not in offer and 'title' not in offer:
            normalized['name'] = f"Offer {offer.get('id', offer.get('offer_id', 'Unknown'))}"

        # Normalize ID
        if 'offer_id' in offer and 'id' not in offer:
            normalized['id'] = offer['offer_id']
        elif 'id' not in offer and 'offer_id' not in offer:
            normalized['id'] = 'unknown'

        # Normalize tracking URL
        url_fields = ['click_url', 'tracking_link', 'url', 'link']
        for field in url_fields:
            if field in offer and 'click_url' not in normalized:
                normalized['click_url'] = offer[field]
                break

        # Normalize icon URL
        icon_fields = ['icon', 'iconUrl', 'icon_url', 'image', 'imageUrl', 'image_url', 'square_image']
        for field in icon_fields:
            if field in offer and 'icon' not in normalized:
                normalized['icon'] = offer[field]
                break

        return normalized

    def ow_upload_json(self):
        filepath, _ = QFileDialog.getOpenFileName(self, "Select JSON Offer File", "", "JSON files (*.json);;All files (*)")
        if not filepath: return

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Use the robust parser
            self.loaded_offers_data = self._parse_offers_from_json(data, os.path.basename(filepath))

            self.ow_json_filename_label.setText(f"<font color='#ffe6a7'><b>Loaded:</b> {os.path.basename(filepath)} ({len(self.loaded_offers_data)} offers)</font>")
            self._log_activity(f"Successfully loaded {len(self.loaded_offers_data)} offers from '{os.path.basename(filepath)}'.", "SUCCESS")
            self._ow_display_offers()

        except json.JSONDecodeError as e:
            QMessageBox.critical(self, "JSON Error", f"Failed to parse JSON file: {str(e)}")
            self._log_activity(f"Failed to parse JSON file '{os.path.basename(filepath)}': {str(e)}", "ERROR")
            self.ow_json_filename_label.setText("<font color='#ffe6a7'><b>JSON Parse Error.</b></font>")
        except ValueError as e:
            QMessageBox.critical(self, "Format Error", str(e))
            self._log_activity(f"Format error in '{os.path.basename(filepath)}': {str(e)}", "ERROR")
            self.ow_json_filename_label.setText("<font color='#ffe6a7'><b>Format Error.</b></font>")
        except Exception as e:
            QMessageBox.critical(self, "File Error", f"Error reading file: {e}")
            self._log_activity(f"Error reading file '{os.path.basename(filepath)}': {e}", "ERROR")
            self.ow_json_filename_label.setText(f"<font color='#ffe6a7'><b>File Read Error:</b> {e}</font>")

    def ow_fetch_by_sid(self):
        sid = self.ow_sid_entry.text().strip()
        if not sid:
            QMessageBox.warning(self, "Input Required", "Please enter a Subscriber ID (SID).")
            return

        self._log_activity(f"Attempting to fetch Besitos offers for SID: {sid}", "INFO")
        self.ow_json_filename_label.setText(f"<font color='#ffe6a7'><b>Fetching for SID:</b> {sid}...</font>")
        self.ow_fetch_sid_btn.setEnabled(False)
        self.ow_upload_btn.setEnabled(False)

        self.fetch_sid_worker = GenericWorker(self._ow_do_fetch_by_sid_request, sid)
        self.fetch_sid_worker.result.connect(self._ow_handle_fetch_sid_result)
        self.fetch_sid_worker.error.connect(self._ow_handle_fetch_sid_error)
        self.fetch_sid_worker.finished.connect(lambda: [
            self.ow_fetch_sid_btn.setEnabled(True),
            self.ow_upload_btn.setEnabled(True)
        ])
        self.fetch_sid_worker.start()

    def _ow_do_fetch_by_sid_request(self, sid):
        # This function runs in the worker thread
        # Get selected limit from combo box
        selected_limit = int(self.ow_limit_combo.currentText())

        params = {
            'channel': BESITOS_CHANNEL,
            'subscriber_id': sid,
            'partner_id': BESITOS_PARTNER_ID,
            'offset': 0,
            'limit': selected_limit  # Use selected limit
        }

        # Get selected user agent from combo box
        user_agent_selection = self.ow_user_agent_combo.currentText()
        if "iOS" in user_agent_selection:
            user_agent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
        else:  # Android
            user_agent = 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'

        headers = {'User-Agent': user_agent}
        api_url = BESITOS_API_BASE_URL

        response = requests.get(api_url, params=params, headers=headers, timeout=20)
        response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
        data = response.json()

        if not isinstance(data, dict) or "offers" not in data or not isinstance(data["offers"], list):
            # It seems Besitos might return a list directly if offers exist, or an object with "offers":[] if none
            if isinstance(data, list): # If API returns a list of offers directly
                return {"offers": data} # Wrap it to match expected structure
            raise ValueError("Invalid API response format. Expected an object with 'offers' array or a direct list of offers.")
        return data


    @Slot(object)
    def _ow_handle_fetch_sid_result(self, data):
        sid = self.ow_sid_entry.text().strip() # Get SID again for logging/display
        try:
            # Use the robust parser for API data too
            self.loaded_offers_data = self._parse_offers_from_json(data, f"API_SID_{sid}")
            self.ow_json_filename_label.setText(f"<font color='#ffe6a7'><b>Fetched</b> {len(self.loaded_offers_data)} offers for SID: {sid}</font>")
            self._log_activity(f"Successfully fetched {len(self.loaded_offers_data)} offers for SID: {sid}.", "SUCCESS")
            self._ow_display_offers()
        except Exception as e:
            self._log_activity(f"Error parsing fetched data for SID {sid}: {str(e)}", "ERROR")
            self.ow_json_filename_label.setText(f"<font color='#ffe6a7'><b>Parse Error:</b> {str(e)[:50]}...</font>")

    @Slot(str)
    def _ow_handle_fetch_sid_error(self, error_message):
        sid = self.ow_sid_entry.text().strip()
        QMessageBox.critical(self, "API Fetch Error", f"Failed to fetch offers: {error_message}")
        self._log_activity(f"API fetch failed for SID {sid}: {error_message}", "ERROR")
        self.ow_json_filename_label.setText(f"<font color='#ffe6a7'><b>API Fetch Error:</b> {error_message[:100]}</font>")

    def _ow_display_offers(self):
        self._clear_layout(self.ow_offers_layout)
        if not self.loaded_offers_data:
            self.ow_offers_layout.addWidget(QLabel("No offers loaded or found."))
            return

        for offer_data in self.loaded_offers_data:
            offer_frame_container = QFrame() # Container to hold the button and the details frame
            offer_frame_container.setFrameShape(QFrame.Shape.StyledPanel)
            offer_main_layout = QVBoxLayout(offer_frame_container)

            # Enhanced Offer Header with Icon
            offer_header_layout = QHBoxLayout()

            # Add offer icon if available
            icon_url = offer_data.get('icon', offer_data.get('icon_url', offer_data.get('image_url', offer_data.get('square_image', ''))))
            icon_label = QLabel()
            icon_label.setFixedSize(56, 56)
            icon_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #bb9457;
                    border-radius: 8px;
                    background-color: #6f1d1b;
                    padding: 4px;
                    font-size: 24px;
                }
            """)

            # Set appropriate icon based on offer type or use default
            if icon_url:
                icon_label.setText("🎯")  # Default app icon
                icon_label.setToolTip(f"Icon URL: {icon_url}")
                # Try to determine app type from name for better icons
                offer_name_lower = offer_data.get('name', '').lower()
                if any(word in offer_name_lower for word in ['game', 'play', 'casino', 'slot']):
                    icon_label.setText("🎮")
                elif any(word in offer_name_lower for word in ['shop', 'buy', 'store', 'purchase']):
                    icon_label.setText("🛒")
                elif any(word in offer_name_lower for word in ['finance', 'bank', 'money', 'loan', 'credit']):
                    icon_label.setText("💰")
                elif any(word in offer_name_lower for word in ['food', 'delivery', 'restaurant', 'eat']):
                    icon_label.setText("🍔")
                elif any(word in offer_name_lower for word in ['fitness', 'health', 'workout', 'exercise']):
                    icon_label.setText("💪")
                elif any(word in offer_name_lower for word in ['dating', 'match', 'meet']):
                    icon_label.setText("💕")
                elif any(word in offer_name_lower for word in ['music', 'audio', 'sound', 'radio']):
                    icon_label.setText("🎵")
                elif any(word in offer_name_lower for word in ['video', 'stream', 'watch', 'tv']):
                    icon_label.setText("📺")
            else:
                icon_label.setText("📱")  # Default mobile app icon

            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            offer_header_layout.addWidget(icon_label)

            # Offer info section
            offer_info_layout = QVBoxLayout()
            offer_name = offer_data.get('name', offer_data.get('title', 'Unnamed Offer'))
            offer_id = offer_data.get('id', offer_data.get('offer_id', 'N/A'))

            # Main title with enhanced styling
            title_label = QLabel(f"<b style='font-size: 12pt; color: #ffe6a7;'>{offer_name}</b>")
            title_label.setWordWrap(True)
            offer_info_layout.addWidget(title_label)

            # Subtitle with ID and additional info
            subtitle_parts = [f"ID: {offer_id}"]
            if 'platform' in offer_data:
                subtitle_parts.append(f"Platform: {offer_data['platform']}")
            if 'payout' in offer_data:
                subtitle_parts.append(f"Payout: {offer_data['payout']}")
            if 'amount' in offer_data:
                subtitle_parts.append(f"Amount: {offer_data['amount']}")
            if 'payout_amount_micros' in offer_data:
                payout_dollars = offer_data['payout_amount_micros'] / 1000000.0
                subtitle_parts.append(f"Payout: ${payout_dollars:.2f}")

            subtitle_label = QLabel(f"<span style='color: #bb9457; font-size: 9pt;'>{' | '.join(subtitle_parts)}</span>")
            subtitle_label.setWordWrap(True)
            offer_info_layout.addWidget(subtitle_label)

            # Add icon URL as clickable link if present
            if icon_url:
                icon_link_label = QLabel(f"<a href='{icon_url}' style='color: #99582a; font-size: 8pt;'>View Icon</a>")
                icon_link_label.setOpenExternalLinks(True)
                offer_info_layout.addWidget(icon_link_label)

            offer_header_layout.addLayout(offer_info_layout)
            offer_header_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
            offer_main_layout.addLayout(offer_header_layout)

            # Button to fetch TxID - this button will be replaced by details once fetched
            fetch_tx_button = QPushButton("🔍 Fetch Transaction ID & Generate Callbacks")
            offer_main_layout.addWidget(fetch_tx_button)

            # Placeholder for details to be populated after fetching TxID
            details_widget = QWidget() # This widget will hold TxID info and goals
            details_layout = QVBoxLayout(details_widget)
            details_widget.setVisible(False) # Initially hidden
            offer_main_layout.addWidget(details_widget)

            fetch_tx_button.clicked.connect(
                lambda: self.ow_trigger_fetch_transaction_id(offer_data, fetch_tx_button, details_widget, details_layout)
            )
            self.ow_offers_layout.addWidget(offer_frame_container)

    def ow_trigger_fetch_transaction_id(self, offer_data, button_to_hide, details_widget, details_layout):
        initial_url = offer_data.get('click_url', offer_data.get('tracking_link')) # Common keys
        if not initial_url:
            QMessageBox.warning(self, "Missing URL", f"Offer '{offer_data.get('name', 'N/A')}' has no tracking link.")
            self._log_activity(f"Offer '{offer_data.get('name', 'N/A')}' has no tracking link.", "ERROR")
            return

        button_to_hide.setText("Processing... Following redirects...")
        button_to_hide.setEnabled(False)
        
        self._log_activity(f"Fetching TxID for '{offer_data.get('name', 'N/A')}': {initial_url}", "INFO")

        self.txid_worker = GenericWorker(self._ow_do_fetch_txid_request, initial_url)
        self.txid_worker.result.connect(lambda result_data: self._ow_handle_txid_result(
            result_data, offer_data, button_to_hide, details_widget, details_layout
        ))
        self.txid_worker.error.connect(lambda error_msg: self._ow_handle_txid_error(
            error_msg, offer_data, button_to_hide, details_layout
        ))
        # No finished signal needed to re-enable button as it's replaced/hidden
        self.txid_worker.start()

    def _ow_do_fetch_txid_request(self, initial_url):
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
        try:
            response = requests.get(initial_url, headers=headers, allow_redirects=True, timeout=25)
        except requests.exceptions.Timeout:
            raise TimeoutError(f"Request timed out after 25s for URL: {initial_url}")
        except requests.exceptions.TooManyRedirects:
             raise ConnectionError(f"Too many redirects for URL: {initial_url}")
        except requests.exceptions.RequestException as e:
            raise ConnectionError(f"Network error for URL {initial_url}: {e}")
            
        final_url = response.url
        
        tx_id = None
        mmp_type = "Unknown"
        final_url_parsed = urlparse(final_url)
        final_query_params = parse_qs(final_url_parsed.query)

        # Adjust detection
        if "adjust.com" in final_url_parsed.netloc or "adj." in final_url_parsed.netloc:
            mmp_type = "Adjust"
            install_cb = final_query_params.get('install_callback', [None])[0]
            if install_cb:
                tx_id = parse_qs(urlparse(unquote_plus(install_cb)).query).get('transaction_id', [None])[0]
            if not tx_id:
                for k, v in final_query_params.items():
                    if k.startswith('event_callback_'):
                        tx_id = parse_qs(urlparse(unquote_plus(v[0])).query).get('transaction_id', [None])[0]
                        if tx_id: break
            if not tx_id: tx_id = final_query_params.get('click_id', [None])[0]
            if not tx_id: tx_id = final_query_params.get('transaction_id', [None])[0]

        # AppsFlyer detection
        elif "appsflyer.com" in final_url_parsed.netloc or ".onelink.me" in final_url_parsed.netloc:
            mmp_type = "AppsFlyer"
            tx_id = final_query_params.get('clickid', [None])[0]
        elif final_url_parsed.scheme == "market":
            mmp_type = "AppsFlyer (market)"
            referrer_str = final_query_params.get('referrer', [None])[0]
            if referrer_str:
                ref_params = parse_qs(referrer_str)
                tx_id = ref_params.get('af_tranid', [None])[0]
                if not tx_id: tx_id = ref_params.get('clickid', [None])[0]
        
        # Generic fallback
        if not tx_id:
            mmp_type = "Generic/Unknown (final URL parsed)"
            tx_id = final_query_params.get('clickid', [None])[0]
            if not tx_id: tx_id = final_query_params.get('click_id', [None])[0]
            if not tx_id: tx_id = final_query_params.get('transaction_id', [None])[0]
            if not tx_id: # Last resort: check path for common patterns (less reliable)
                path_parts = [p for p in final_url_parsed.path.split('/') if p]
                for part in path_parts:
                    if len(part) > 10 and (part.isalnum() or '-' in part or '_' in part): # Heuristic for ID-like string
                        # This is very speculative, be careful
                        # tx_id = part 
                        pass


        return {"tx_id": tx_id, "mmp_type": mmp_type, "final_url": final_url}

    @Slot(object, object, QPushButton, QWidget, QVBoxLayout)
    def _ow_handle_txid_result(self, result_data, offer_data, button_to_hide, details_widget, details_layout):
        button_to_hide.setVisible(False) # Hide the "Fetch TxID" button
        details_widget.setVisible(True) # Show the details area

        tx_id = result_data['tx_id']
        mmp_type = result_data['mmp_type']
        final_url = result_data['final_url']
        
        self._clear_layout(details_layout) # Clear previous details if any

        if tx_id:
            details_layout.addWidget(QLabel(f"<b>Extracted Transaction ID:</b> <font color='#ffe6a7' style='background-color: #99582a; padding: 4px; border-radius: 3px;'>{tx_id}</font>"))
            self._log_activity(f"Extracted TxID '{tx_id}' ({mmp_type}) for '{offer_data.get('name', 'N/A')}'", "SUCCESS")
        else:
            no_txid_label = QLabel("<b>Transaction ID not found in final URL.</b>")
            no_txid_label.setStyleSheet("color: #432818; background-color: #ffe6a7; padding: 6px; border-radius: 4px; font-weight: bold;")
            details_layout.addWidget(no_txid_label)
            self._log_activity(f"No TxID found for '{offer_data.get('name', 'N/A')}' in final URL: {final_url}", "WARNING")

        details_layout.addWidget(QLabel(f"<b>Detected MMP Type:</b> {mmp_type}"))
        final_url_label = QLabel(f"<b>Final Tracking Link:</b> {final_url[:120]}{'...' if len(final_url) > 120 else ''}")
        final_url_label.setWordWrap(True)
        details_layout.addWidget(final_url_label)

        if mmp_type.startswith("AppsFlyer"):
            af_note = QLabel("Note: This is an AppsFlyer link. Generated Adjust-style callbacks below are for testing convenience (AppsFlyer events are typically S2S).")
            af_note.setStyleSheet("color: #432818; background-color: #ffe6a7; padding: 8px; border-radius: 6px; font-style: italic; font-weight: 500;")
            af_note.setWordWrap(True)
            details_layout.addWidget(af_note)

        goals = offer_data.get('goals', [])
        if tx_id and goals: # Only show goals if TxID was found for callback generation
            goals_title = QLabel("<b>Offer Goals & Callbacks:</b>")
            goals_title.setStyleSheet("margin-top: 5px;")
            details_layout.addWidget(goals_title)

            for goal in goals:
                goal_frame = QFrame()
                goal_frame.setFrameShape(QFrame.Shape.NoFrame) # No extra border for each goal
                goal_layout = QVBoxLayout(goal_frame)
                goal_layout.setContentsMargins(0,2,0,2)


                goal_text_orig = goal.get('name', goal.get('text', 'N/A'))
                goal_text_display = goal_text_orig.replace('TURBO EARNINGS', '<b><font color="#ffe6a7" style="background-color: #bb9457; padding: 2px 4px; border-radius: 3px;">TURBO EARNINGS</font></b>')
                payout = goal.get('payout_amount_micros', 0) / 1000000.0
                goal_id = goal.get('goal_id', goal.get('id', 'N/A'))
                
                goal_label_text = f"Goal: {goal_text_display} (ID: {goal_id}) - Payout: ${payout:.2f}"
                if 'days_left_to_complete' in goal:
                     goal_label_text += f" (Days left: {goal.get('days_left_to_complete')})"

                goal_layout.addWidget(QLabel(goal_label_text))

                callback_params = {'kashkick_event': '1', 'goal_id': goal_id, 'transaction_id': tx_id}
                encoded_params = "&".join([f"{quote_plus(str(k))}={quote_plus(str(v))}" for k,v in callback_params.items() if v is not None])
                generated_callback_url = f"{ADJUST_WRAPPER_URL}?{encoded_params}"
                
                self._create_pyside_link_display(goal_layout, f"Adj. Callback (Goal {goal_id})", generated_callback_url, "Goal CB")
                details_layout.addWidget(goal_frame)
        elif not goals:
            details_layout.addWidget(QLabel("No goals found for this offer."))


    @Slot(str, object, QPushButton, QVBoxLayout)
    def _ow_handle_txid_error(self, error_message, offer_data, button_to_hide, details_layout):
        button_to_hide.setText("Fetch Transaction ID (Retry)") # Change text back
        button_to_hide.setEnabled(True) # Re-enable button
        # Don't hide button, allow retry. Clear previous details if any.
        self._clear_layout(details_layout)

        err_label = QLabel(f"<b>Error fetching TxID:</b> {error_message}")
        err_label.setStyleSheet("color: #ffe6a7; background-color: #6f1d1b; padding: 8px; border-radius: 6px; font-weight: bold;")
        err_label.setWordWrap(True)
        details_layout.insertWidget(0, err_label) # Insert error at the top of details section
        self._log_activity(f"Error fetching TxID for '{offer_data.get('name', 'N/A')}': {error_message}", "ERROR")


    # --- Helper Functions ---
    def _clear_layout(self, layout):
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                else:
                    sub_layout = item.layout()
                    if sub_layout is not None:
                        self._clear_layout(sub_layout)
                        # sub_layout.deleteLater() # Qt should handle this if parent is deleted

    def copy_to_clipboard(self, text_to_copy, item_name="Text"):
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(text_to_copy)
            self._log_activity(f"Copied {item_name} to clipboard: {text_to_copy[:70]}...", "SUCCESS")
            QMessageBox.information(self, "Copied", f"{item_name} copied to clipboard.")
        except Exception as e:
            self._log_activity(f"Failed to copy {item_name} to clipboard: {e}", "ERROR")
            QMessageBox.critical(self, "Copy Error", f"Could not copy to clipboard: {e}")

    def fire_link(self, url, link_name="Link"):
        try:
            # Ensure URL has a scheme, webbrowser might need it
            if not url.startswith(('http://', 'https://', 'market://')): # Add other schemes if needed
                url_to_open = 'http://' + url # Default to http if scheme missing
            else:
                url_to_open = url
            
            webbrowser.open_new_tab(url_to_open)
            self._log_activity(f"Attempted to fire {link_name}: {url_to_open}", "INFO")
        except Exception as e:
            self._log_activity(f"Error firing {link_name} ({url}): {e}", "ERROR")
            QMessageBox.critical(self, "Fire Error", f"Could not open link: {e}\nURL: {url}")

    def closeEvent(self, event):
        # Clean up any running threads if necessary
        # For GenericWorker, if they are daemonic or parented to QObjects that get deleted,
        # they might terminate. Explicitly quitting them is safer.
        # This is a placeholder, actual thread management might be more complex
        # depending on how many persistent workers you might have.
        # For now, workers are short-lived.
        self._log_activity("Application closing.", "INFO")
        super().closeEvent(event)


# --- Main Execution ---
if __name__ == "__main__":
    app = QApplication(sys.argv)
    # You can set an application icon here if you have one
    # app_icon = QIcon("path/to/your/icon.png")
    # app.setWindowIcon(app_icon)
    
    window = MobileMarketingToolkit()
    window.show()
    sys.exit(app.exec())
