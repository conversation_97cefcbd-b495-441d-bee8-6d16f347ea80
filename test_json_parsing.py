#!/usr/bin/env python3
"""
Test script to verify the robust JSON parsing functionality
"""
import json
import os

def test_json_file(filepath):
    """Test parsing a JSON file"""
    print(f"\n🔍 Testing: {filepath}")
    print("=" * 50)
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ JSON loaded successfully")
        print(f"📊 Data type: {type(data)}")
        
        if isinstance(data, dict):
            print(f"🔑 Keys: {list(data.keys())}")
            for key, value in data.items():
                if isinstance(value, list):
                    print(f"   📋 {key}: {len(value)} items")
                elif isinstance(value, dict):
                    print(f"   📦 {key}: dict with {len(value)} keys")
                else:
                    print(f"   📄 {key}: {type(value).__name__}")
        elif isinstance(data, list):
            print(f"📋 Direct array with {len(data)} items")
            if data and isinstance(data[0], dict):
                print(f"🔑 First item keys: {list(data[0].keys())}")
        
        # Test our parsing logic
        offers = []
        
        # Handle direct array format (like Monlix)
        if isinstance(data, list):
            offers = data
            print(f"✅ Detected direct array format with {len(offers)} offers.")
        
        # Handle object formats
        elif isinstance(data, dict):
            # Try different possible keys for offers
            possible_keys = ['offers', 'offers_array', 'data', 'results', 'items']
            
            for key in possible_keys:
                if key in data and isinstance(data[key], list):
                    offers = data[key]
                    print(f"✅ Detected '{key}' format with {len(offers)} offers.")
                    break
        
        if offers:
            print(f"🎯 Successfully extracted {len(offers)} offers")
            if offers:
                first_offer = offers[0]
                print(f"📱 First offer keys: {list(first_offer.keys()) if isinstance(first_offer, dict) else 'Not a dict'}")
                if isinstance(first_offer, dict):
                    name = first_offer.get('name', first_offer.get('title', 'N/A'))
                    offer_id = first_offer.get('id', first_offer.get('offer_id', 'N/A'))
                    print(f"   📝 Name: {name}")
                    print(f"   🆔 ID: {offer_id}")
                    
                    # Check for icon fields
                    icon_fields = ['icon', 'iconUrl', 'icon_url', 'image', 'imageUrl', 'image_url', 'square_image']
                    for field in icon_fields:
                        if field in first_offer:
                            print(f"   🖼️ Icon field '{field}': {first_offer[field][:50]}...")
                            break
        else:
            print("❌ No offers found")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Test all JSON files in the OFFER_WALL_JSONS directory"""
    json_dir = "OFFER_WALL_JSONS"
    
    if not os.path.exists(json_dir):
        print(f"❌ Directory {json_dir} not found")
        return
    
    json_files = [f for f in os.listdir(json_dir) if f.endswith('.json')]
    
    if not json_files:
        print(f"❌ No JSON files found in {json_dir}")
        return
    
    print(f"🚀 Testing {len(json_files)} JSON files from {json_dir}")
    
    for json_file in json_files:
        filepath = os.path.join(json_dir, json_file)
        test_json_file(filepath)
    
    print(f"\n✅ Testing complete!")

if __name__ == "__main__":
    main()
